# AI Chatbot Setup Guide

This guide will help you set up the AI chatbot feature in your Bordj El Mokrani Flutter app.

## Overview

The app now includes a modern AI chatbot that can:
- Answer questions about <PERSON>rd<PERSON> <PERSON>
- Help users navigate the app
- Provide general assistance
- Support multiple languages (English, French, Arabic)
- Work with multiple AI providers

## Supported AI Providers

### 1. OpenAI (Recommended)
- **Models**: GPT-3.5 Turbo, GPT-4
- **Cost**: Pay per use
- **Quality**: Excellent
- **Setup**: Get API key from [OpenAI Platform](https://platform.openai.com/api-keys)

### 2. Google Gemini
- **Models**: Gemini Pro
- **Cost**: Free tier available
- **Quality**: Very good
- **Setup**: Get API key from [Google AI Studio](https://makersuite.google.com/app/apikey)

### 3. Anthropic Claude
- **Models**: Claude 3 Sonnet
- **Cost**: Pay per use
- **Quality**: Excellent
- **Setup**: Get API key from [Anthropic Console](https://console.anthropic.com/)

### 4. <PERSON><PERSON><PERSON> (Local AI)
- **Models**: Llama 2, <PERSON><PERSON><PERSON>, and others
- **Cost**: Free (runs locally)
- **Quality**: Good
- **Setup**: Install [Ollama](https://ollama.ai/) and download models

## Quick Setup

### Step 1: Choose Your AI Provider
Pick one of the providers above based on your needs and budget.

### Step 2: Get API Key (if needed)
- For OpenAI, Gemini, or Claude: Follow the links above to get an API key
- For Ollama: No API key needed, but you need to install it locally

### Step 3: Configure the App
1. Open the file: `lib/config/ai_config.dart`
2. Replace the placeholder API key with your actual key
3. Set the `defaultProvider` to your chosen provider

Example for OpenAI:
```dart
static const String openAIApiKey = 'sk-your-actual-api-key-here';
static const AIProvider defaultProvider = AIProvider.openai;
```

### Step 4: Restart the App
After configuration, restart the app to enable the chatbot feature.

## Detailed Setup Instructions

### OpenAI Setup
1. Visit [OpenAI Platform](https://platform.openai.com/api-keys)
2. Create an account or sign in
3. Navigate to API Keys section
4. Click "Create new secret key"
5. Copy the generated key
6. In `lib/config/ai_config.dart`, replace:
   ```dart
   static const String openAIApiKey = 'your-openai-api-key-here';
   ```
   with:
   ```dart
   static const String openAIApiKey = 'sk-your-actual-key-here';
   ```
7. Set the default provider:
   ```dart
   static const AIProvider defaultProvider = AIProvider.openai;
   ```

### Google Gemini Setup
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the generated key
5. In `lib/config/ai_config.dart`, replace:
   ```dart
   static const String geminiApiKey = 'your-gemini-api-key-here';
   ```
   with your actual key
6. Set the default provider:
   ```dart
   static const AIProvider defaultProvider = AIProvider.gemini;
   ```

### Anthropic Claude Setup
1. Visit [Anthropic Console](https://console.anthropic.com/)
2. Create an account or sign in
3. Navigate to API Keys
4. Generate a new API key
5. Copy the key
6. In `lib/config/ai_config.dart`, replace:
   ```dart
   static const String claudeApiKey = 'your-claude-api-key-here';
   ```
   with your actual key
7. Set the default provider:
   ```dart
   static const AIProvider defaultProvider = AIProvider.claude;
   ```

### Ollama Setup (Local AI)
1. Visit [Ollama.ai](https://ollama.ai/) and download the installer
2. Install Ollama on your computer
3. Open terminal/command prompt
4. Download a model: `ollama pull llama2`
5. Start Ollama service (usually starts automatically)
6. In `lib/config/ai_config.dart`, set:
   ```dart
   static const AIProvider defaultProvider = AIProvider.ollama;
   ```
7. No API key needed for Ollama!

## Features

### Modern UI
- Clean, responsive chat interface
- Typing indicators
- Message timestamps
- Markdown support for AI responses
- Dark theme support

### Multilingual Support
- English, French, and Arabic translations
- RTL support for Arabic
- Localized AI responses

### Smart Features
- Conversation history
- Message retry functionality
- Quick suggestion chips
- Error handling with user-friendly messages

### Responsive Design
- Works on all screen sizes
- Optimized for mobile and tablet
- Smooth animations and transitions

## Troubleshooting

### "AI Setup Required" Screen
If you see this screen, it means the AI provider is not configured properly:
1. Check that you've set the correct API key
2. Verify the default provider is set correctly
3. Restart the app after making changes

### API Errors
- **Invalid API Key**: Double-check your API key is correct
- **Rate Limits**: You may have exceeded your API usage limits
- **Network Issues**: Check your internet connection

### Ollama Issues
- **Connection Failed**: Make sure Ollama is running
- **Model Not Found**: Download the model with `ollama pull model-name`
- **Port Issues**: Check if port 11434 is available

## Cost Considerations

### OpenAI
- GPT-3.5 Turbo: ~$0.002 per 1K tokens
- GPT-4: ~$0.03 per 1K tokens
- Typical conversation: $0.01-0.05

### Google Gemini
- Free tier: 60 requests per minute
- Paid tier: Very competitive pricing

### Anthropic Claude
- Similar to OpenAI pricing
- High quality responses

### Ollama
- Completely free
- Requires local compute resources
- No internet needed after setup

## Security Notes

- Never commit API keys to version control
- Consider using environment variables for production
- Monitor your API usage regularly
- Set up billing alerts with your AI provider

## Support

If you encounter issues:
1. Check the in-app setup instructions
2. Verify your API key is valid
3. Check the provider's status page
4. Restart the app after configuration changes

The chatbot feature enhances user experience by providing instant assistance and information about Bordj El Mokrani, making your app more interactive and helpful.
