import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/chat_message.dart';
import '../services/ai_chat_service.dart';

class ChatProvider extends ChangeNotifier {
  final AIChatService _aiService;

  List<ChatSession> _sessions = [];
  ChatSession? _currentSession;
  bool _isLoading = false;
  String? _error;

  // Dynamic system prompts based on language
  static String _getSystemPrompt(BuildContext context) {
    final locale = Localizations.localeOf(context);

    switch (locale.languageCode) {
      case 'ar':
        return '''
أنت مساعد ذكي مفيد لتطبيق برج المقراني. يمكنك مساعدة المستخدمين في:
- معلومات حول برج المقراني (التاريخ، الثقافة، السياحة)
- التنقل في التطبيق وميزاته
- الأسئلة العامة والمساعدة
- توصيات لزيارة المنطقة

يرجى أن تكون ودودًا ومفيدًا ومعلوماتيًا. إذا كنت لا تعرف شيئًا محددًا حول برج المقراني، كن صادقًا حول ذلك ولكن حاول تقديم معلومات عامة مفيدة.

تحدث باللغة العربية الجزائرية (الدارجة) عندما يكون ذلك مناسبًا، واستخدم العربية الفصحى للمعلومات الرسمية.
''';

      case 'fr':
        return '''
Vous êtes un assistant IA utile pour l'application Bordj El Mokrani. Vous pouvez aider les utilisateurs avec :
- Informations sur Bordj El Mokrani (histoire, culture, tourisme)
- Navigation dans l'application et ses fonctionnalités
- Questions générales et assistance
- Recommandations pour visiter la région

Veuillez être amical, informatif et utile. Si vous ne connaissez pas quelque chose de spécifique sur Bordj El Mokrani, soyez honnête à ce sujet mais essayez de fournir des informations générales utiles.

Répondez en français et utilisez un ton chaleureux et accueillant typique de l'hospitalité algérienne.
''';

      default: // English
        return '''
You are a helpful AI assistant for the Bordj El Mokrani app. You can help users with:
- Information about Bordj El Mokrani (history, culture, tourism)
- App navigation and features
- General questions and assistance
- Recommendations for visiting the area

Please be friendly, informative, and helpful. If you don't know something specific about Bordj El Mokrani, be honest about it but try to provide general helpful information.

Respond in English with a warm and welcoming tone that reflects Algerian hospitality.
''';
    }
  }

  ChatProvider({required AIChatService aiService}) : _aiService = aiService {
    _loadSessions();
  }

  // Getters
  List<ChatSession> get sessions => List.unmodifiable(_sessions);
  ChatSession? get currentSession => _currentSession;
  bool get isLoading => _isLoading;
  String? get error => _error;
  List<ChatMessage> get currentMessages => _currentSession?.messages ?? [];

  // Get localized welcome message
  static String _getWelcomeMessage(BuildContext context) {
    final locale = Localizations.localeOf(context);

    switch (locale.languageCode) {
      case 'ar':
        return 'أهلاً وسهلاً! أنا مساعدك الذكي لبرج المقراني. كيف يمكنني مساعدتك اليوم؟';
      case 'fr':
        return 'Bienvenue ! Je suis votre assistant IA pour Bordj El Mokrani. Comment puis-je vous aider aujourd\'hui ?';
      default:
        return 'Welcome! I\'m your AI assistant for Bordj El Mokrani. How can I help you today?';
    }
  }

  // Create a new chat session
  Future<void> createNewSession({String? title, BuildContext? context}) async {
    final session = ChatSession(
      title: title ?? 'New Chat ${_sessions.length + 1}',
    );

    _sessions.insert(0, session);
    _currentSession = session;

    // Add welcome message
    final welcomeMessage = ChatMessage.systemMessage(
      context != null ? _getWelcomeMessage(context) : 'Welcome! I\'m your AI assistant for Bordj El Mokrani. How can I help you today?'
    );

    _currentSession!.messages.add(welcomeMessage);

    await _saveSessions();
    notifyListeners();
  }

  // Switch to an existing session
  void switchToSession(String sessionId) {
    final session = _sessions.firstWhere(
      (s) => s.id == sessionId,
      orElse: () => _sessions.first,
    );

    _currentSession = session;
    notifyListeners();
  }

  // Delete a session
  Future<void> deleteSession(String sessionId) async {
    _sessions.removeWhere((s) => s.id == sessionId);

    if (_currentSession?.id == sessionId) {
      _currentSession = _sessions.isNotEmpty ? _sessions.first : null;
    }

    await _saveSessions();
    notifyListeners();
  }

  // Send a message
  Future<void> sendMessage(String content, BuildContext context) async {
    if (content.trim().isEmpty) return;

    // Get system prompt before any async operations
    final systemPrompt = _getSystemPrompt(context);

    // Ensure we have a current session
    if (_currentSession == null) {
      await createNewSession(context: context);
    }

    // Add user message
    final userMessage = ChatMessage.userText(content.trim());
    _currentSession!.messages.add(userMessage);

    // Add typing indicator
    final typingMessage = ChatMessage.typing();
    _currentSession!.messages.add(typingMessage);

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {

      // Get AI response
      final response = await _aiService.sendMessage(
        message: content,
        conversationHistory: _currentSession!.messages
            .where((m) => m.type == MessageType.text && !m.isLoading)
            .toList(),
        systemPrompt: systemPrompt,
      );

      // Remove typing indicator
      _currentSession!.messages.removeWhere((m) => m.isLoading);

      // Add AI response
      _currentSession!.messages.add(response);

      // Update session title if it's the first real conversation
      if (_currentSession!.messages.where((m) => m.sender == MessageSender.user).length == 1) {
        final newTitle = _generateSessionTitle(content);
        _currentSession = _currentSession!.copyWith(title: newTitle);

        // Update in sessions list
        final index = _sessions.indexWhere((s) => s.id == _currentSession!.id);
        if (index != -1) {
          _sessions[index] = _currentSession!;
        }
      }

      // Update last message time
      _currentSession = _currentSession!.copyWith(lastMessageAt: DateTime.now());

      await _saveSessions();

    } catch (e) {
      // Remove typing indicator
      _currentSession!.messages.removeWhere((m) => m.isLoading);

      _error = 'Failed to send message: ${e.toString()}';

      // Add error message
      final errorMessage = ChatMessage.botText(
        'Sorry, I encountered an error. Please check your internet connection and try again.'
      );
      _currentSession!.messages.add(errorMessage);

      debugPrint('Chat error: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Retry last message
  Future<void> retryLastMessage(BuildContext context) async {
    if (_currentSession == null || _currentSession!.messages.isEmpty) return;

    // Find the last user message
    final lastUserMessage = _currentSession!.messages
        .lastWhere(
          (m) => m.sender == MessageSender.user,
          orElse: () => ChatMessage.userText(''),
        );

    if (lastUserMessage.content.isNotEmpty) {
      // Remove the last bot message if it exists
      if (_currentSession!.messages.last.sender == MessageSender.bot) {
        _currentSession!.messages.removeLast();
      }

      await sendMessage(lastUserMessage.content, context);
    }
  }

  // Get localized clear message
  static String _getClearMessage(BuildContext context) {
    final locale = Localizations.localeOf(context);

    switch (locale.languageCode) {
      case 'ar':
        return 'تم مسح المحادثة. كيف يمكنني مساعدتك اليوم؟';
      case 'fr':
        return 'Chat effacé. Comment puis-je vous aider aujourd\'hui ?';
      default:
        return 'Chat cleared. How can I help you today?';
    }
  }

  // Clear current session
  void clearCurrentSession(BuildContext context) {
    if (_currentSession != null) {
      _currentSession!.messages.clear();

      // Add welcome message
      final welcomeMessage = ChatMessage.systemMessage(
        _getClearMessage(context)
      );
      _currentSession!.messages.add(welcomeMessage);

      _saveSessions();
      notifyListeners();
    }
  }

  // Generate a title for the session based on the first message
  String _generateSessionTitle(String firstMessage) {
    if (firstMessage.length <= 30) {
      return firstMessage;
    }

    // Take first 30 characters and add ellipsis
    return '${firstMessage.substring(0, 30)}...';
  }

  // Save sessions to local storage
  Future<void> _saveSessions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionsJson = _sessions.map((s) => s.toJson()).toList();
      await prefs.setString('chat_sessions', jsonEncode(sessionsJson));

      if (_currentSession != null) {
        await prefs.setString('current_session_id', _currentSession!.id);
      }
    } catch (e) {
      debugPrint('Failed to save chat sessions: $e');
    }
  }

  // Load sessions from local storage
  Future<void> _loadSessions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionsString = prefs.getString('chat_sessions');
      final currentSessionId = prefs.getString('current_session_id');

      if (sessionsString != null) {
        final sessionsList = jsonDecode(sessionsString) as List;
        _sessions = sessionsList
            .map((json) => ChatSession.fromJson(json))
            .toList();

        if (currentSessionId != null) {
          _currentSession = _sessions
              .where((s) => s.id == currentSessionId)
              .firstOrNull;
        }

        // If no current session but we have sessions, use the first one
        if (_currentSession == null && _sessions.isNotEmpty) {
          _currentSession = _sessions.first;
        }
      }

      // If no sessions exist, create a default one
      if (_sessions.isEmpty) {
        await createNewSession(title: 'Welcome Chat');
      }

    } catch (e) {
      debugPrint('Failed to load chat sessions: $e');
      // Create a default session if loading fails
      await createNewSession(title: 'Welcome Chat');
    }
  }

  @override
  void dispose() {
    _aiService.dispose();
    super.dispose();
  }
}
