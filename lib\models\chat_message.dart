import 'package:uuid/uuid.dart';

enum MessageType {
  text,
  image,
  file,
  typing,
}

enum MessageSender {
  user,
  bot,
  system,
}

class ChatMessage {
  final String id;
  final String content;
  final MessageType type;
  final MessageSender sender;
  final DateTime timestamp;
  final bool isLoading;
  final String? imageUrl;
  final String? fileName;
  final String? fileUrl;
  final Map<String, dynamic>? metadata;

  ChatMessage({
    String? id,
    required this.content,
    required this.type,
    required this.sender,
    DateTime? timestamp,
    this.isLoading = false,
    this.imageUrl,
    this.fileName,
    this.fileUrl,
    this.metadata,
  })  : id = id ?? const Uuid().v4(),
        timestamp = timestamp ?? DateTime.now();

  ChatMessage copyWith({
    String? id,
    String? content,
    MessageType? type,
    MessageSender? sender,
    DateTime? timestamp,
    bool? isLoading,
    String? imageUrl,
    String? fileName,
    String? fileUrl,
    Map<String, dynamic>? metadata,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      content: content ?? this.content,
      type: type ?? this.type,
      sender: sender ?? this.sender,
      timestamp: timestamp ?? this.timestamp,
      isLoading: isLoading ?? this.isLoading,
      imageUrl: imageUrl ?? this.imageUrl,
      fileName: fileName ?? this.fileName,
      fileUrl: fileUrl ?? this.fileUrl,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'type': type.name,
      'sender': sender.name,
      'timestamp': timestamp.toIso8601String(),
      'isLoading': isLoading,
      'imageUrl': imageUrl,
      'fileName': fileName,
      'fileUrl': fileUrl,
      'metadata': metadata,
    };
  }

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'],
      content: json['content'],
      type: MessageType.values.firstWhere((e) => e.name == json['type']),
      sender: MessageSender.values.firstWhere((e) => e.name == json['sender']),
      timestamp: DateTime.parse(json['timestamp']),
      isLoading: json['isLoading'] ?? false,
      imageUrl: json['imageUrl'],
      fileName: json['fileName'],
      fileUrl: json['fileUrl'],
      metadata: json['metadata'],
    );
  }

  // Factory constructors for common message types
  factory ChatMessage.userText(String content) {
    return ChatMessage(
      content: content,
      type: MessageType.text,
      sender: MessageSender.user,
    );
  }

  factory ChatMessage.botText(String content) {
    return ChatMessage(
      content: content,
      type: MessageType.text,
      sender: MessageSender.bot,
    );
  }

  factory ChatMessage.typing() {
    return ChatMessage(
      content: '',
      type: MessageType.typing,
      sender: MessageSender.bot,
      isLoading: true,
    );
  }

  factory ChatMessage.systemMessage(String content) {
    return ChatMessage(
      content: content,
      type: MessageType.text,
      sender: MessageSender.system,
    );
  }
}

class ChatSession {
  final String id;
  final String title;
  final DateTime createdAt;
  final DateTime lastMessageAt;
  final List<ChatMessage> messages;
  final Map<String, dynamic>? metadata;

  ChatSession({
    String? id,
    required this.title,
    DateTime? createdAt,
    DateTime? lastMessageAt,
    List<ChatMessage>? messages,
    this.metadata,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        lastMessageAt = lastMessageAt ?? DateTime.now(),
        messages = messages ?? [];

  ChatSession copyWith({
    String? id,
    String? title,
    DateTime? createdAt,
    DateTime? lastMessageAt,
    List<ChatMessage>? messages,
    Map<String, dynamic>? metadata,
  }) {
    return ChatSession(
      id: id ?? this.id,
      title: title ?? this.title,
      createdAt: createdAt ?? this.createdAt,
      lastMessageAt: lastMessageAt ?? this.lastMessageAt,
      messages: messages ?? this.messages,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'createdAt': createdAt.toIso8601String(),
      'lastMessageAt': lastMessageAt.toIso8601String(),
      'messages': messages.map((m) => m.toJson()).toList(),
      'metadata': metadata,
    };
  }

  factory ChatSession.fromJson(Map<String, dynamic> json) {
    return ChatSession(
      id: json['id'],
      title: json['title'],
      createdAt: DateTime.parse(json['createdAt']),
      lastMessageAt: DateTime.parse(json['lastMessageAt']),
      messages: (json['messages'] as List?)
              ?.map((m) => ChatMessage.fromJson(m))
              .toList() ??
          [],
      metadata: json['metadata'],
    );
  }
}
