import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import '../config/ai_config.dart';
import '../extensions/string_extensions.dart';

class AISetupScreen extends StatelessWidget {
  const AISetupScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'AI Setup Required'.tr(context),
          style: GoogleFonts.montserrat(
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: colorScheme.surface,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    colorScheme.primaryContainer.withOpacity(0.3),
                    colorScheme.secondaryContainer.withOpacity(0.3),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  Container(
                    width: 64,
                    height: 64,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(32),
                      boxShadow: [
                        BoxShadow(
                          color: colorScheme.primary.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(32),
                      child: Image.asset(
                        'assets/images/iconchat.jpeg',
                        width: 64,
                        height: 64,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            width: 64,
                            height: 64,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [colorScheme.primary, colorScheme.secondary],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: BorderRadius.circular(32),
                            ),
                            child: Icon(
                              Icons.auto_awesome,
                              size: 32,
                              color: colorScheme.onPrimary,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'AI Chatbot Setup'.tr(context),
                    style: GoogleFonts.playfairDisplay(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Configure an AI provider to enable the chatbot feature'.tr(context),
                    style: GoogleFonts.montserrat(
                      fontSize: 16,
                      color: colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // Provider Options
            Text(
              'Choose an AI Provider:'.tr(context),
              style: GoogleFonts.montserrat(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: colorScheme.onSurface,
              ),
            ),

            const SizedBox(height: 16),

            _buildProviderCard(
              context,
              title: 'OpenAI (Recommended)',
              description: 'GPT-3.5 Turbo or GPT-4 models',
              icon: Icons.psychology,
              color: Colors.green,
              url: 'https://platform.openai.com/api-keys',
              steps: [
                '1. Visit platform.openai.com/api-keys',
                '2. Create an account or sign in',
                '3. Generate a new API key',
                '4. Copy the API key',
                '5. Paste it in lib/config/ai_config.dart',
              ],
            ),

            const SizedBox(height: 16),

            _buildProviderCard(
              context,
              title: 'Google Gemini',
              description: 'Google\'s latest AI model',
              icon: Icons.auto_awesome,
              color: Colors.blue,
              url: 'https://makersuite.google.com/app/apikey',
              steps: [
                '1. Visit makersuite.google.com/app/apikey',
                '2. Sign in with Google account',
                '3. Create a new API key',
                '4. Copy the API key',
                '5. Paste it in lib/config/ai_config.dart',
              ],
            ),

            const SizedBox(height: 16),

            _buildProviderCard(
              context,
              title: 'Anthropic Claude',
              description: 'Claude 3 Sonnet model',
              icon: Icons.chat_bubble_outline,
              color: Colors.orange,
              url: 'https://console.anthropic.com/',
              steps: [
                '1. Visit console.anthropic.com',
                '2. Create an account or sign in',
                '3. Generate a new API key',
                '4. Copy the API key',
                '5. Paste it in lib/config/ai_config.dart',
              ],
            ),

            const SizedBox(height: 16),

            _buildProviderCard(
              context,
              title: 'Ollama (Local AI)',
              description: 'Run AI models locally (Free)',
              icon: Icons.computer,
              color: Colors.purple,
              url: 'https://ollama.ai/',
              steps: [
                '1. Visit ollama.ai and download',
                '2. Install Ollama on your computer',
                '3. Run: ollama pull llama2',
                '4. Set provider to Ollama in config',
                '5. No API key required!',
              ],
            ),

            const SizedBox(height: 32),

            // Configuration Instructions
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: colorScheme.surfaceVariant.withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: colorScheme.outline.withOpacity(0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.settings_outlined,
                        color: colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Configuration File'.tr(context),
                        style: GoogleFonts.montserrat(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Edit the file: lib/config/ai_config.dart',
                    style: GoogleFonts.sourceCodePro(
                      fontSize: 14,
                      color: colorScheme.onSurfaceVariant,
                      backgroundColor: colorScheme.surface,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: () => _copyConfigPath(context),
                    icon: const Icon(Icons.copy),
                    label: Text('Copy File Path'.tr(context)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colorScheme.primaryContainer,
                      foregroundColor: colorScheme.onPrimaryContainer,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Restart Note
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.errorContainer.withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: colorScheme.error,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'After configuring, restart the app to enable the chatbot feature.'.tr(context),
                      style: GoogleFonts.montserrat(
                        fontSize: 14,
                        color: colorScheme.onErrorContainer,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProviderCard(
    BuildContext context, {
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required String url,
    required List<String> steps,
  }) {
    final colorScheme = Theme.of(context).colorScheme;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ExpansionTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: GoogleFonts.montserrat(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        subtitle: Text(
          description,
          style: GoogleFonts.montserrat(
            fontSize: 14,
            color: colorScheme.onSurfaceVariant,
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ...steps.map((step) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Text(
                    step,
                    style: GoogleFonts.montserrat(
                      fontSize: 14,
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                )),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () => _launchURL(url),
                  icon: const Icon(Icons.open_in_new),
                  label: Text('Get API Key'.tr(context)),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: color,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _launchURL(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  void _copyConfigPath(BuildContext context) {
    Clipboard.setData(const ClipboardData(text: 'lib/config/ai_config.dart'));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('File path copied to clipboard'.tr(context)),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
