import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../models/chat_message.dart';

enum AIProvider {
  openai,
  gemini,
  claude,
  ollama, // For local AI
}

class AIConfig {
  final AIProvider provider;
  final String apiKey;
  final String? baseUrl;
  final String model;
  final Map<String, dynamic>? additionalParams;

  const AIConfig({
    required this.provider,
    required this.apiKey,
    this.baseUrl,
    required this.model,
    this.additionalParams,
  });
}

class AIChatService {
  static const Duration _timeout = Duration(seconds: 30);

  // Default configurations for different providers
  static const Map<AIProvider, String> _defaultModels = {
    AIProvider.openai: 'gpt-3.5-turbo',
    AIProvider.gemini: 'gemini-pro',
    AIProvider.claude: 'claude-3-sonnet-20240229',
    AIProvider.ollama: 'llama2',
  };

  static const Map<AIProvider, String> _defaultBaseUrls = {
    AIProvider.openai: 'https://api.openai.com/v1',
    AIProvider.gemini: 'https://generativelanguage.googleapis.com/v1beta',
    AIProvider.claude: 'https://api.anthropic.com/v1',
    AIProvider.ollama: 'http://localhost:11434/api',
  };

  final AIConfig config;
  final http.Client _client = http.Client();

  AIChatService({required this.config});

  // Factory constructors for different providers
  factory AIChatService.openAI({
    required String apiKey,
    String model = 'gpt-3.5-turbo',
  }) {
    return AIChatService(
      config: AIConfig(
        provider: AIProvider.openai,
        apiKey: apiKey,
        model: model,
      ),
    );
  }

  factory AIChatService.gemini({
    required String apiKey,
    String model = 'gemini-2.0-flash',
  }) {
    return AIChatService(
      config: AIConfig(
        provider: AIProvider.gemini,
        apiKey: apiKey,
        model: model,
      ),
    );
  }

  factory AIChatService.claude({
    required String apiKey,
    String model = 'claude-3-sonnet-20240229',
  }) {
    return AIChatService(
      config: AIConfig(
        provider: AIProvider.claude,
        apiKey: apiKey,
        model: model,
      ),
    );
  }

  factory AIChatService.ollama({
    String baseUrl = 'http://localhost:11434/api',
    String model = 'llama2',
  }) {
    return AIChatService(
      config: AIConfig(
        provider: AIProvider.ollama,
        apiKey: '', // Ollama doesn't require API key
        baseUrl: baseUrl,
        model: model,
      ),
    );
  }

  Future<ChatMessage> sendMessage({
    required String message,
    List<ChatMessage>? conversationHistory,
    String? systemPrompt,
  }) async {
    try {
      switch (config.provider) {
        case AIProvider.openai:
          return await _sendOpenAIMessage(message, conversationHistory, systemPrompt);
        case AIProvider.gemini:
          return await _sendGeminiMessage(message, conversationHistory, systemPrompt);
        case AIProvider.claude:
          return await _sendClaudeMessage(message, conversationHistory, systemPrompt);
        case AIProvider.ollama:
          return await _sendOllamaMessage(message, conversationHistory, systemPrompt);
      }
    } catch (e) {
      debugPrint('AI Service Error: $e');
      return ChatMessage.botText('Sorry, I encountered an error. Please try again.');
    }
  }

  Future<ChatMessage> _sendOpenAIMessage(
    String message,
    List<ChatMessage>? history,
    String? systemPrompt,
  ) async {
    final url = Uri.parse('${_defaultBaseUrls[AIProvider.openai]}/chat/completions');

    final messages = <Map<String, String>>[];

    if (systemPrompt != null) {
      messages.add({'role': 'system', 'content': systemPrompt});
    }

    if (history != null) {
      for (final msg in history) {
        if (msg.type == MessageType.text && msg.sender != MessageSender.system) {
          messages.add({
            'role': msg.sender == MessageSender.user ? 'user' : 'assistant',
            'content': msg.content,
          });
        }
      }
    }

    messages.add({'role': 'user', 'content': message});

    final response = await _client.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ${config.apiKey}',
      },
      body: jsonEncode({
        'model': config.model,
        'messages': messages,
        'max_tokens': 1000,
        'temperature': 0.7,
      }),
    ).timeout(_timeout);

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      final content = data['choices'][0]['message']['content'];
      return ChatMessage.botText(content);
    } else {
      throw Exception('OpenAI API error: ${response.statusCode}');
    }
  }

  Future<ChatMessage> _sendGeminiMessage(
    String message,
    List<ChatMessage>? history,
    String? systemPrompt,
  ) async {
    final url = Uri.parse(
      '${_defaultBaseUrls[AIProvider.gemini]}/models/${config.model}:generateContent?key=${config.apiKey}'
    );

    String fullPrompt = message;
    if (systemPrompt != null) {
      fullPrompt = '$systemPrompt\n\n$message';
    }

    final response = await _client.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'contents': [
          {
            'parts': [
              {'text': fullPrompt}
            ]
          }
        ],
        'generationConfig': {
          'temperature': 0.7,
          'maxOutputTokens': 1000,
        }
      }),
    ).timeout(_timeout);

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      final content = data['candidates'][0]['content']['parts'][0]['text'];
      return ChatMessage.botText(content);
    } else {
      throw Exception('Gemini API error: ${response.statusCode}');
    }
  }

  Future<ChatMessage> _sendClaudeMessage(
    String message,
    List<ChatMessage>? history,
    String? systemPrompt,
  ) async {
    final url = Uri.parse('${_defaultBaseUrls[AIProvider.claude]}/messages');

    final messages = <Map<String, String>>[];

    if (history != null) {
      for (final msg in history) {
        if (msg.type == MessageType.text && msg.sender != MessageSender.system) {
          messages.add({
            'role': msg.sender == MessageSender.user ? 'user' : 'assistant',
            'content': msg.content,
          });
        }
      }
    }

    messages.add({'role': 'user', 'content': message});

    final body = {
      'model': config.model,
      'max_tokens': 1000,
      'messages': messages,
    };

    if (systemPrompt != null) {
      body['system'] = systemPrompt;
    }

    final response = await _client.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': config.apiKey,
        'anthropic-version': '2023-06-01',
      },
      body: jsonEncode(body),
    ).timeout(_timeout);

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      final content = data['content'][0]['text'];
      return ChatMessage.botText(content);
    } else {
      throw Exception('Claude API error: ${response.statusCode}');
    }
  }

  Future<ChatMessage> _sendOllamaMessage(
    String message,
    List<ChatMessage>? history,
    String? systemPrompt,
  ) async {
    final baseUrl = config.baseUrl ?? _defaultBaseUrls[AIProvider.ollama]!;
    final url = Uri.parse('$baseUrl/generate');

    String prompt = message;
    if (systemPrompt != null) {
      prompt = '$systemPrompt\n\nUser: $message\nAssistant:';
    }

    final response = await _client.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'model': config.model,
        'prompt': prompt,
        'stream': false,
      }),
    ).timeout(_timeout);

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      final content = data['response'];
      return ChatMessage.botText(content);
    } else {
      throw Exception('Ollama API error: ${response.statusCode}');
    }
  }

  void dispose() {
    _client.close();
  }
}
