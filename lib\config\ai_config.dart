import '../services/ai_chat_service.dart';

/// AI Configuration for the chatbot
///
/// To use the AI chatbot feature, you need to configure at least one AI provider.
/// Replace the placeholder API keys with your actual API keys.
///
/// Supported providers:
/// - OpenAI (GPT models)
/// - Google Gemini
/// - Anthropic Claude
/// - Ollama (local AI, no API key required)
class AIConfiguration {
  // OpenAI Configuration
  static const String openAIApiKey = 'your-openai-api-key-here';
  static const String openAIModel = 'gpt-3.5-turbo'; // or 'gpt-4'

  // Google Gemini Configuration
  static const String geminiApiKey = 'AIzaSyB70K4h6pR6QrPVpT4FMG4VfrQjb4_W6_I';
  static const String geminiModel = 'gemini-2.0-flash'; // Updated to use the latest model

  // Anthropic Claude Configuration
  static const String claudeApiKey = 'your-claude-api-key-here';
  static const String claudeModel = 'claude-3-sonnet-20240229';

  // Ollama Configuration (for local AI)
  static const String ollamaBaseUrl = 'http://localhost:11434/api';
  static const String ollamaModel = 'llama2'; // or any model you have installed

  // Default provider to use
  static const AIProvider defaultProvider = AIProvider.gemini;

  /// Get the configured AI service based on the default provider
  static AIChatService getDefaultAIService() {
    switch (defaultProvider) {
      case AIProvider.openai:
        if (openAIApiKey == 'your-openai-api-key-here') {
          throw Exception(
            'OpenAI API key not configured. Please set your API key in lib/config/ai_config.dart'
          );
        }
        return AIChatService.openAI(
          apiKey: openAIApiKey,
          model: openAIModel,
        );

      case AIProvider.gemini:
        if (geminiApiKey == 'your-gemini-api-key-here') {
          throw Exception(
            'Gemini API key not configured. Please set your API key in lib/config/ai_config.dart'
          );
        }
        return AIChatService.gemini(
          apiKey: geminiApiKey,
          model: geminiModel,
        );

      case AIProvider.claude:
        if (claudeApiKey == 'your-claude-api-key-here') {
          throw Exception(
            'Claude API key not configured. Please set your API key in lib/config/ai_config.dart'
          );
        }
        return AIChatService.claude(
          apiKey: claudeApiKey,
          model: claudeModel,
        );

      case AIProvider.ollama:
        return AIChatService.ollama(
          baseUrl: ollamaBaseUrl,
          model: ollamaModel,
        );
    }
  }

  /// Check if the current configuration is valid
  static bool isConfigured() {
    try {
      getDefaultAIService();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get configuration instructions for the user
  static String getConfigurationInstructions() {
    return '''
To use the AI chatbot feature, you need to configure an AI provider:

1. OpenAI (Recommended):
   - Get an API key from: https://platform.openai.com/api-keys
   - Replace 'your-openai-api-key-here' with your actual API key
   - Set defaultProvider to AIProvider.openai

2. Google Gemini:
   - Get an API key from: https://makersuite.google.com/app/apikey
   - Replace 'your-gemini-api-key-here' with your actual API key
   - Set defaultProvider to AIProvider.gemini

3. Anthropic Claude:
   - Get an API key from: https://console.anthropic.com/
   - Replace 'your-claude-api-key-here' with your actual API key
   - Set defaultProvider to AIProvider.claude

4. Ollama (Local AI):
   - Install Ollama from: https://ollama.ai/
   - Pull a model: ollama pull llama2
   - Set defaultProvider to AIProvider.ollama
   - No API key required

Edit the file: lib/config/ai_config.dart
''';
  }
}
